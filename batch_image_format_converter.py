#!/usr/bin/env python3
"""
批量图片格式转换工具
用于转换types目录下各个题型images文件夹中的图片格式
支持的格式：jpg, jpeg, png, gif, webp, bmp, tiff
"""

import os
import sys
import shutil
import argparse
from PIL import Image
import glob
from pathlib import Path

# 支持的图片格式（基于test.py和test3.py中的定义）
SUPPORTED_INPUT_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif']
SUPPORTED_OUTPUT_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff']

def get_all_image_dirs():
    """获取所有题型下的images目录"""
    types_dir = "types"
    image_dirs = []
    
    if not os.path.exists(types_dir):
        print(f"错误：找不到{types_dir}目录")
        return []
    
    for item in os.listdir(types_dir):
        item_path = os.path.join(types_dir, item)
        if os.path.isdir(item_path):
            images_path = os.path.join(item_path, "images")
            if os.path.exists(images_path) and os.path.isdir(images_path):
                image_dirs.append((item, images_path))
    
    return image_dirs

def get_image_files(directory):
    """获取目录中的所有图片文件"""
    image_files = []
    for ext in SUPPORTED_INPUT_FORMATS:
        pattern = os.path.join(directory, f"*{ext}")
        image_files.extend(glob.glob(pattern))
        # 也搜索大写扩展名
        pattern = os.path.join(directory, f"*{ext.upper()}")
        image_files.extend(glob.glob(pattern))
    
    return sorted(list(set(image_files)))  # 去重并排序

def convert_image(input_path, output_path, target_format, quality=95):
    """转换单个图片格式"""
    try:
        with Image.open(input_path) as img:
            # 处理RGBA模式的图片转换为RGB（对于不支持透明度的格式）
            if target_format.lower() in ['jpg', 'jpeg'] and img.mode in ['RGBA', 'LA']:
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                else:
                    background.paste(img)
                img = background
            elif target_format.lower() not in ['png', 'gif', 'webp'] and img.mode in ['RGBA', 'LA']:
                # 对于其他不支持透明度的格式，转换为RGB
                img = img.convert('RGB')
            
            # 保存图片
            save_kwargs = {}
            if target_format.lower() in ['jpg', 'jpeg']:
                save_kwargs['quality'] = quality
                save_kwargs['optimize'] = True
            elif target_format.lower() == 'png':
                save_kwargs['optimize'] = True
            elif target_format.lower() == 'webp':
                save_kwargs['quality'] = quality
                save_kwargs['method'] = 6  # 最佳压缩
            
            img.save(output_path, format=target_format.upper(), **save_kwargs)
            return True, None
    except Exception as e:
        return False, str(e)

def backup_directory(source_dir):
    """备份目录"""
    backup_dir = f"{source_dir}_backup"
    counter = 1
    while os.path.exists(backup_dir):
        backup_dir = f"{source_dir}_backup_{counter}"
        counter += 1
    
    try:
        shutil.copytree(source_dir, backup_dir)
        return backup_dir
    except Exception as e:
        print(f"备份失败：{e}")
        return None

def convert_images_in_directory(images_dir, target_format, backup=True, replace=False, quality=95):
    """转换目录中的所有图片"""
    image_files = get_image_files(images_dir)
    
    if not image_files:
        return 0, 0, []
    
    # 备份原始文件（如果需要）
    backup_dir = None
    if backup and not replace:
        backup_dir = backup_directory(images_dir)
        if backup_dir:
            print(f"  已备份到：{backup_dir}")
        else:
            print("  警告：备份失败，继续转换...")
    
    converted_count = 0
    failed_count = 0
    failed_files = []
    
    for input_path in image_files:
        filename = os.path.basename(input_path)
        name_without_ext = os.path.splitext(filename)[0]
        
        if replace:
            # 替换原文件
            output_path = os.path.join(images_dir, f"{name_without_ext}.{target_format}")
            if output_path != input_path:  # 只有当格式不同时才转换
                success, error = convert_image(input_path, output_path, target_format, quality)
                if success:
                    # 删除原文件
                    try:
                        os.remove(input_path)
                        converted_count += 1
                        print(f"    ✓ {filename} -> {os.path.basename(output_path)}")
                    except Exception as e:
                        print(f"    ✗ 删除原文件失败 {filename}: {e}")
                        failed_count += 1
                        failed_files.append((filename, f"删除原文件失败: {e}"))
                else:
                    print(f"    ✗ 转换失败 {filename}: {error}")
                    failed_count += 1
                    failed_files.append((filename, error))
            else:
                print(f"    - {filename} (已是目标格式)")
        else:
            # 创建新文件
            output_path = os.path.join(images_dir, f"{name_without_ext}.{target_format}")
            success, error = convert_image(input_path, output_path, target_format, quality)
            if success:
                converted_count += 1
                print(f"    ✓ {filename} -> {os.path.basename(output_path)}")
            else:
                print(f"    ✗ 转换失败 {filename}: {error}")
                failed_count += 1
                failed_files.append((filename, error))
    
    return converted_count, failed_count, failed_files

def main():
    parser = argparse.ArgumentParser(description='批量转换题型images文件夹中的图片格式')
    parser.add_argument('target_format', choices=SUPPORTED_OUTPUT_FORMATS,
                       help='目标图片格式')
    parser.add_argument('--types', nargs='+',
                       help='指定要处理的题型（默认处理所有题型）')
    parser.add_argument('--no-backup', action='store_true',
                       help='不创建备份（仅在--replace模式下有效）')
    parser.add_argument('--replace', action='store_true',
                       help='替换原文件而不是创建新文件')
    parser.add_argument('--quality', type=int, default=95,
                       help='JPEG/WebP质量 (1-100, 默认95)')
    parser.add_argument('--dry-run', action='store_true',
                       help='预览模式，不实际转换文件')

    args = parser.parse_args()

    # 获取所有images目录
    all_image_dirs = get_all_image_dirs()

    if not all_image_dirs:
        print("未找到任何包含images文件夹的题型目录")
        return

    # 过滤指定的题型
    if args.types:
        filtered_dirs = []
        for type_name, images_path in all_image_dirs:
            if type_name in args.types:
                filtered_dirs.append((type_name, images_path))

        # 检查是否有未找到的题型
        found_types = [type_name for type_name, _ in filtered_dirs]
        missing_types = set(args.types) - set(found_types)
        if missing_types:
            print(f"警告：未找到以下题型：{', '.join(missing_types)}")

        all_image_dirs = filtered_dirs

    if not all_image_dirs:
        print("没有找到要处理的题型目录")
        return

    print(f"批量图片格式转换工具")
    print(f"目标格式：{args.target_format.upper()}")
    print(f"处理模式：{'替换原文件' if args.replace else '创建新文件'}")
    print(f"备份设置：{'不备份' if args.no_backup else '自动备份'}")
    if args.target_format.lower() in ['jpg', 'jpeg', 'webp']:
        print(f"图片质量：{args.quality}")
    print(f"预览模式：{'是' if args.dry_run else '否'}")
    print("=" * 60)

    total_converted = 0
    total_failed = 0
    all_failed_files = []

    for type_name, images_path in all_image_dirs:
        print(f"\n处理题型：{type_name}")
        print(f"目录：{images_path}")

        # 统计当前目录的图片文件
        image_files = get_image_files(images_path)
        if not image_files:
            print("  没有找到图片文件")
            continue

        print(f"  找到 {len(image_files)} 个图片文件")

        if args.dry_run:
            # 预览模式，只显示会处理的文件
            for img_path in image_files:
                filename = os.path.basename(img_path)
                name_without_ext = os.path.splitext(filename)[0]
                target_filename = f"{name_without_ext}.{args.target_format}"
                if args.replace:
                    if target_filename != filename:
                        print(f"    预览：{filename} -> {target_filename} (替换)")
                    else:
                        print(f"    预览：{filename} (已是目标格式)")
                else:
                    print(f"    预览：{filename} -> {target_filename} (新建)")
        else:
            # 实际转换
            converted, failed, failed_files = convert_images_in_directory(
                images_path, args.target_format,
                backup=not args.no_backup,
                replace=args.replace,
                quality=args.quality
            )

            total_converted += converted
            total_failed += failed
            all_failed_files.extend([(type_name, f, e) for f, e in failed_files])

            print(f"  转换完成：{converted} 成功，{failed} 失败")

    # 总结
    print("\n" + "=" * 60)
    if args.dry_run:
        print("预览完成")
        print("使用 --dry-run 参数查看将要执行的操作")
        print("移除 --dry-run 参数开始实际转换")
    else:
        print(f"批量转换完成")
        print(f"总计：{total_converted} 个文件转换成功，{total_failed} 个文件转换失败")

        if all_failed_files:
            print(f"\n失败的文件：")
            for type_name, filename, error in all_failed_files:
                print(f"  {type_name}/{filename}: {error}")

if __name__ == "__main__":
    main()
