#!/usr/bin/env python3
"""
批量图片格式转换工具
用于转换types目录下各个题型images文件夹中的图片格式
支持的格式：jpg, jpeg, png, gif, webp, bmp, tiff
"""

import os
import sys
import shutil
import argparse
from PIL import Image
import glob
from pathlib import Path

# 支持的图片格式（基于test.py和test3.py中的定义）
SUPPORTED_INPUT_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif']
SUPPORTED_OUTPUT_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff']

def get_all_image_dirs():
    """获取所有题型下的images目录"""
    types_dir = "types"
    image_dirs = []
    
    if not os.path.exists(types_dir):
        print(f"错误：找不到{types_dir}目录")
        return []
    
    for item in os.listdir(types_dir):
        item_path = os.path.join(types_dir, item)
        if os.path.isdir(item_path):
            images_path = os.path.join(item_path, "images")
            if os.path.exists(images_path) and os.path.isdir(images_path):
                image_dirs.append((item, images_path))
    
    return image_dirs

def get_image_files(directory):
    """获取目录中的所有图片文件"""
    image_files = []
    for ext in SUPPORTED_INPUT_FORMATS:
        pattern = os.path.join(directory, f"*{ext}")
        image_files.extend(glob.glob(pattern))
        # 也搜索大写扩展名
        pattern = os.path.join(directory, f"*{ext.upper()}")
        image_files.extend(glob.glob(pattern))
    
    return sorted(list(set(image_files)))  # 去重并排序

def convert_image(input_path, output_path, target_format, quality=95):
    """转换单个图片格式"""
    try:
        with Image.open(input_path) as img:
            # 处理RGBA模式的图片转换为RGB（对于不支持透明度的格式）
            if target_format.lower() in ['jpg', 'jpeg'] and img.mode in ['RGBA', 'LA']:
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                else:
                    background.paste(img)
                img = background
            elif target_format.lower() not in ['png', 'gif', 'webp'] and img.mode in ['RGBA', 'LA']:
                # 对于其他不支持透明度的格式，转换为RGB
                img = img.convert('RGB')
            
            # 保存图片
            save_kwargs = {}
            if target_format.lower() in ['jpg', 'jpeg']:
                save_kwargs['quality'] = quality
                save_kwargs['optimize'] = True
            elif target_format.lower() == 'png':
                save_kwargs['optimize'] = True
            elif target_format.lower() == 'webp':
                save_kwargs['quality'] = quality
                save_kwargs['method'] = 6  # 最佳压缩
            
            img.save(output_path, format=target_format.upper(), **save_kwargs)
            return True, None
    except Exception as e:
        return False, str(e)

def backup_directory(source_dir):
    """备份目录"""
    backup_dir = f"{source_dir}_backup"
    counter = 1
    while os.path.exists(backup_dir):
        backup_dir = f"{source_dir}_backup_{counter}"
        counter += 1
    
    try:
        shutil.copytree(source_dir, backup_dir)
        return backup_dir
    except Exception as e:
        print(f"备份失败：{e}")
        return None

def convert_images_in_directory(images_dir, target_format, backup=True, replace=False, quality=95):
    """转换目录中的所有图片"""
    image_files = get_image_files(images_dir)
    
    if not image_files:
        return 0, 0, []
    
    # 备份原始文件（如果需要）
    backup_dir = None
    if backup and not replace:
        backup_dir = backup_directory(images_dir)
        if backup_dir:
            print(f"  已备份到：{backup_dir}")
        else:
            print("  警告：备份失败，继续转换...")
    
    converted_count = 0
    failed_count = 0
    failed_files = []
    
    for input_path in image_files:
        filename = os.path.basename(input_path)
        name_without_ext = os.path.splitext(filename)[0]
        
        if replace:
            # 替换原文件
            output_path = os.path.join(images_dir, f"{name_without_ext}.{target_format}")
            if output_path != input_path:  # 只有当格式不同时才转换
                success, error = convert_image(input_path, output_path, target_format, quality)
                if success:
                    # 删除原文件
                    try:
                        os.remove(input_path)
                        converted_count += 1
                        print(f"    ✓ {filename} -> {os.path.basename(output_path)}")
                    except Exception as e:
                        print(f"    ✗ 删除原文件失败 {filename}: {e}")
                        failed_count += 1
                        failed_files.append((filename, f"删除原文件失败: {e}"))
                else:
                    print(f"    ✗ 转换失败 {filename}: {error}")
                    failed_count += 1
                    failed_files.append((filename, error))
            else:
                print(f"    - {filename} (已是目标格式)")
        else:
            # 创建新文件
            output_path = os.path.join(images_dir, f"{name_without_ext}.{target_format}")
            success, error = convert_image(input_path, output_path, target_format, quality)
            if success:
                converted_count += 1
                print(f"    ✓ {filename} -> {os.path.basename(output_path)}")
            else:
                print(f"    ✗ 转换失败 {filename}: {error}")
                failed_count += 1
                failed_files.append((filename, error))
    
    return converted_count, failed_count, failed_files

def select_types_interactive(all_image_dirs):
    """交互式选择题型"""
    print("\n可用的题型：")
    print("0. 全部题型")

    for i, (type_name, images_path) in enumerate(all_image_dirs, 1):
        image_count = len(get_image_files(images_path))
        print(f"{i}. {type_name} ({image_count} 张图片)")

    while True:
        try:
            choice = input(f"\n请选择题型 (0-{len(all_image_dirs)})，多个选择用逗号分隔: ").strip()

            if not choice:
                continue

            choices = [int(x.strip()) for x in choice.split(',')]

            # 验证选择
            if any(c < 0 or c > len(all_image_dirs) for c in choices):
                print(f"错误：请输入 0 到 {len(all_image_dirs)} 之间的数字")
                continue

            # 处理选择
            if 0 in choices:
                return all_image_dirs  # 选择全部
            else:
                selected_dirs = []
                for c in choices:
                    selected_dirs.append(all_image_dirs[c-1])
                return selected_dirs

        except ValueError:
            print("错误：请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return []

def select_format_interactive():
    """交互式选择目标格式"""
    print("\n支持的图片格式：")
    for i, fmt in enumerate(SUPPORTED_OUTPUT_FORMATS, 1):
        print(f"{i}. {fmt.upper()}")

    while True:
        try:
            choice = input(f"\n请选择目标格式 (1-{len(SUPPORTED_OUTPUT_FORMATS)}): ").strip()

            if not choice:
                continue

            choice_num = int(choice)

            if choice_num < 1 or choice_num > len(SUPPORTED_OUTPUT_FORMATS):
                print(f"错误：请输入 1 到 {len(SUPPORTED_OUTPUT_FORMATS)} 之间的数字")
                continue

            return SUPPORTED_OUTPUT_FORMATS[choice_num - 1]

        except ValueError:
            print("错误：请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return None

def get_conversion_options():
    """获取转换选项"""
    print("\n转换选项：")

    # 选择处理模式
    while True:
        mode = input("处理模式 (1=创建新文件, 2=替换原文件) [1]: ").strip()
        if not mode:
            mode = "1"
        if mode in ["1", "2"]:
            replace = (mode == "2")
            break
        print("错误：请输入 1 或 2")

    # 如果是替换模式，询问是否备份
    backup = True
    if replace:
        while True:
            backup_choice = input("是否创建备份 (y/n) [y]: ").strip().lower()
            if not backup_choice:
                backup_choice = "y"
            if backup_choice in ["y", "yes", "是"]:
                backup = True
                break
            elif backup_choice in ["n", "no", "否"]:
                backup = False
                break
            print("错误：请输入 y 或 n")

    # 图片质量设置
    quality = 95
    quality_input = input("JPEG/WebP 图片质量 (1-100) [95]: ").strip()
    if quality_input:
        try:
            quality = int(quality_input)
            if quality < 1 or quality > 100:
                print("警告：质量值超出范围，使用默认值 95")
                quality = 95
        except ValueError:
            print("警告：无效的质量值，使用默认值 95")

    # 预览模式
    while True:
        preview = input("是否先预览 (y/n) [y]: ").strip().lower()
        if not preview:
            preview = "y"
        if preview in ["y", "yes", "是"]:
            dry_run = True
            break
        elif preview in ["n", "no", "否"]:
            dry_run = False
            break
        print("错误：请输入 y 或 n")

    return replace, backup, quality, dry_run

def main():
    print("=" * 60)
    print("批量图片格式转换工具")
    print("=" * 60)

    # 获取所有images目录
    all_image_dirs = get_all_image_dirs()

    if not all_image_dirs:
        print("未找到任何包含images文件夹的题型目录")
        return

    # 交互式选择题型
    selected_dirs = select_types_interactive(all_image_dirs)
    if not selected_dirs:
        print("未选择任何题型，退出")
        return

    # 交互式选择格式
    target_format = select_format_interactive()
    if not target_format:
        print("未选择目标格式，退出")
        return

    # 获取转换选项
    replace, backup, quality, dry_run = get_conversion_options()

    # 显示配置摘要
    print("\n" + "=" * 60)
    print("转换配置摘要：")
    print(f"选择的题型：{len(selected_dirs)} 个")
    for type_name, _ in selected_dirs:
        print(f"  - {type_name}")
    print(f"目标格式：{target_format.upper()}")
    print(f"处理模式：{'替换原文件' if replace else '创建新文件'}")
    if replace:
        print(f"备份设置：{'不备份' if not backup else '自动备份'}")
    if target_format.lower() in ['jpg', 'jpeg', 'webp']:
        print(f"图片质量：{quality}")
    print(f"预览模式：{'是' if dry_run else '否'}")
    print("=" * 60)

    if not dry_run:
        confirm = input("\n确认开始转换？(y/n): ").strip().lower()
        if confirm not in ["y", "yes", "是"]:
            print("用户取消操作")
            return

    total_converted = 0
    total_failed = 0
    all_failed_files = []

    for type_name, images_path in selected_dirs:
        print(f"\n处理题型：{type_name}")
        print(f"目录：{images_path}")

        # 统计当前目录的图片文件
        image_files = get_image_files(images_path)
        if not image_files:
            print("  没有找到图片文件")
            continue

        print(f"  找到 {len(image_files)} 个图片文件")

        if dry_run:
            # 预览模式，只显示会处理的文件
            for img_path in image_files:
                filename = os.path.basename(img_path)
                name_without_ext = os.path.splitext(filename)[0]
                target_filename = f"{name_without_ext}.{target_format}"
                if replace:
                    if target_filename != filename:
                        print(f"    预览：{filename} -> {target_filename} (替换)")
                    else:
                        print(f"    预览：{filename} (已是目标格式)")
                else:
                    print(f"    预览：{filename} -> {target_filename} (新建)")
        else:
            # 实际转换
            converted, failed, failed_files = convert_images_in_directory(
                images_path, target_format,
                backup=backup,
                replace=replace,
                quality=quality
            )

            total_converted += converted
            total_failed += failed
            all_failed_files.extend([(type_name, f, e) for f, e in failed_files])

            print(f"  转换完成：{converted} 成功，{failed} 失败")

    # 总结
    print("\n" + "=" * 60)
    if dry_run:
        print("预览完成")
        print("重新运行脚本并选择'否'来开始实际转换")
    else:
        print(f"批量转换完成")
        print(f"总计：{total_converted} 个文件转换成功，{total_failed} 个文件转换失败")

        if all_failed_files:
            print(f"\n失败的文件：")
            for type_name, filename, error in all_failed_files:
                print(f"  {type_name}/{filename}: {error}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断操作，程序退出")
