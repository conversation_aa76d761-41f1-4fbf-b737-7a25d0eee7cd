#!/usr/bin/env python3
"""
合并batch_configs中的题型json文件
排除图表题(tubiaoti)、画图题(huatuti)、连线题(lianxianti)
以及带有java和python后缀的文件
"""

import json
import os

def merge_batch_configs():
    """合并batch_configs"""
    
    # 要包含的题型文件（排除图表题、画图题、连线题）
    target_files = [
        'danxuanti.json',           # 单选题
        'panduanti.json',           # 判断题
        'tiankongti.json',          # 填空题
        'shuxuejisuanti.json',      # 数学计算题
        'shuxueyingyongti.json',    # 数学应用题
        'jiandandesizeyunsuan.json', # 简单的四则运算
        'tukaxuanzeti.json',        # 图卡选择题
        'tukapanduanti.json'        # 图卡判断题
    ]
    
    # 检查还有哪些其他的题型文件
    batch_configs_dir = "batch_configs"
    all_files = []
    if os.path.exists(batch_configs_dir):
        for filename in os.listdir(batch_configs_dir):
            if filename.endswith('.json') and not any(suffix in filename for suffix in ['_java', '_python', 'copy']):
                all_files.append(filename)
    
    print("所有可用的json文件：")
    for f in sorted(all_files):
        print(f"  - {f}")
    
    print(f"\n要合并的文件：")
    for f in target_files:
        print(f"  - {f}")
    
    # 检查是否有其他需要包含的文件
    other_files = [f for f in all_files if f not in target_files and f not in ['tubiaoti.json', 'huatuti.json', 'lianxianti.json']]
    if other_files:
        print(f"\n其他可能需要包含的文件：")
        for f in other_files:
            print(f"  - {f}")
    
    # 合并配置
    merged_config = {
        "循环次数": 1,
        "batch_configs": []
    }
    
    for filename in target_files:
        filepath = os.path.join(batch_configs_dir, filename)
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 提取batch_configs部分
                if 'batch_configs' in config and config['batch_configs']:
                    for batch_config in config['batch_configs']:
                        merged_config['batch_configs'].append(batch_config)
                    print(f"✓ 已合并 {filename}")
                else:
                    print(f"✗ {filename} 格式不正确，跳过")
                    
            except Exception as e:
                print(f"✗ 读取 {filename} 失败: {e}")
        else:
            print(f"✗ 找不到文件 {filename}")
    
    # 保存合并后的配置
    output_file = os.path.join(batch_configs_dir, "total.json")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(merged_config, f, ensure_ascii=False, indent=2)
        
        print(f"\n✓ 合并完成，保存到: {output_file}")
        print(f"总共合并了 {len(merged_config['batch_configs'])} 个配置")
        print(f"循环次数设置为: {merged_config['循环次数']}")
        
        # 显示每个配置的题型信息
        print(f"\n合并的配置详情：")
        for i, config in enumerate(merged_config['batch_configs'], 1):
            题型 = config.get('题型', '未知')
            处理模式 = config.get('处理模式', '未知')
            print(f"  {i}. 题型: {题型}, 处理模式: {处理模式}")
            
    except Exception as e:
        print(f"✗ 保存失败: {e}")

if __name__ == "__main__":
    merge_batch_configs()
