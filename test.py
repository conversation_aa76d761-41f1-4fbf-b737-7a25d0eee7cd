# export ARK_API_KEY="YOUR_API_KEY" 查看API KEY 

import os
import base64
import json
import sys
from volcenginesdkarkruntime import Ark
import datetime
import re
import shutil
import markdown
from bs4 import BeautifulSoup
from pypinyin import pinyin, Style
from PIL import Image
import io
import numpy as np
import multiprocessing  # 新增：导入多进程模块
import time  # 新增：用于记录时间
from tqdm import tqdm  # 新增：导入进度条模块

from image_utils import image_to_base64, validate_base64
from yolo_utils import parse_yolo_annotation
from ai_utils import extract_json_from_response
from system_prompt_utils import get_system_prompt_config, build_messages_with_system_prompt

def generate_default_error_json(question_count=1, json_type="answer"):
    """
    生成默认的错误JSON格式
    question_count: 题目数量
    json_type: "answer" 表示答案格式，"boolean" 表示布尔格式
    """
    if json_type == "boolean":
        # 对于test2.py, test3.py, one_stage_test.py，返回false的布尔值
        return json.dumps({f"题目{i}": False for i in range(1, question_count + 1)}, ensure_ascii=False)
    else:
        # 对于test.py，返回"API请求失败"的答案格式
        return json.dumps({f"题目 {i}": "API请求失败" for i in range(1, question_count + 1)}, ensure_ascii=False)

def extract_question_count_from_json(json_str):
    """
    从JSON字符串中提取题目数量
    """
    if not json_str:
        return 1
    try:
        data = json.loads(json_str)
        if isinstance(data, dict):
            return len(data)
    except:
        pass
    return 1

def process_single_image_local(task):
    """
    处理单张图片的本地操作（增强、缩放、base64编码）
    """
    image_path = task['image_path']
    img_filename = task['img_filename']
    use_enhance = task['use_enhance']
    enhance_threshold = task['enhance_threshold']
    scale = task['scale']
    use_pixel_connection = task['use_pixel_connection']

    try:
        base64_image = image_to_base64(image_path, use_enhance=use_enhance,
                                     enhance_threshold=enhance_threshold,
                                     scale=scale, use_pixel_connection=use_pixel_connection)
        return {
            'success': True,
            'img_filename': img_filename,
            'base64_image': base64_image,
            'image_path': image_path
        }
    except Exception as e:
        return {
            'success': False,
            'img_filename': img_filename,
            'base64_image': None,
            'image_path': image_path,
            'error': str(e)
        }

def enhance_marks_to_black(image_data, threshold=200):
    """
    将图片中的灰色或浅黑色标记增强为纯黑色，并将背景转换为纯白色。
    threshold: 阈值，低于该值的像素变为黑色，否则为白色
    """
    # 从内存中的图片数据创建Image对象
    image = Image.open(io.BytesIO(image_data))
    # 1. 转换为灰度图像
    grayscale_image = image.convert('L')
    # 2. 应用阈值，二值化
    threshold_image = grayscale_image.point(lambda p: 0 if p < threshold else 255)
    # 3. 转回RGB（如有需要）
    final_image = threshold_image.convert('RGB')
    # 4. 保存到内存
    buffer = io.BytesIO()
    final_image.save(buffer, format='JPEG')
    return buffer.getvalue()

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_latest_folder(base_dir, prefix="images_"):
    """获取指定目录下以prefix开头的最新文件夹"""
    if not os.path.exists(base_dir):
        return None

    folders = []
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path) and item.startswith(prefix):
            folders.append(item)

    if not folders:
        return None

    # 按文件夹名排序，取最新的（字典序最大的）
    folders.sort()
    return folders[-1]

def get_folder_choice(question_dir):
    """让用户选择图像文件夹"""
    folder_options = {
        "1": "images",
        "2": "OpenCV_result",
        "3": "grounding_result",
        "4": "YOLO_result",
        "5": "YOLO_text_result",
        "6": "manual_result",
        "7": "roboflow_yolo_result",
        "8": "pixel_enhancement_result_java",
        "9": "pixel_enhancement_result_python"
    }

    print("\n请选择图像文件夹：")
    for key, value in folder_options.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入文件夹编号（1-9）：").strip()

        if user_input in folder_options:
            folder_name = folder_options[user_input]
            break
        else:
            print("输入无效，请输入 1-9 的数字")

    # 如果选择的是结果文件夹，需要找到最新的子文件夹
    if folder_name != "images":
        # 特殊处理pixel_enhancement_result文件夹
        if folder_name in ["pixel_enhancement_result_java", "pixel_enhancement_result_python"]:
            result_dir = os.path.join(question_dir, "pixel_enhancement_result")
            
            if folder_name == "pixel_enhancement_result_java":
                # 选择8时，使用java_process_images子文件夹
                subfolder = "java_process_images"
            else:
                # 选择9时，使用python_process_images子文件夹
                subfolder = "python_process_images"
            
            process_dir = os.path.join(result_dir, subfolder)
            if os.path.exists(process_dir) and os.path.isdir(process_dir):
                actual_images_dir = process_dir
                print(f"选择的文件夹：pixel_enhancement_result")
                print(f"子文件夹：{subfolder}")
                print(f"实际图片路径：{actual_images_dir}")
                # 返回相对于question_dir的路径，用于生成正确的markdown图片路径
                relative_path = os.path.join("pixel_enhancement_result", subfolder).replace("\\", "/")
                return actual_images_dir, relative_path
            else:
                print(f"错误：pixel_enhancement_result 文件夹下没有找到 {subfolder} 子文件夹！")
                return None, None
        else:
            # 其他结果文件夹使用原有的逻辑
            result_dir = os.path.join(question_dir, folder_name)
            latest_folder = get_latest_folder(result_dir)
            if latest_folder:
                actual_images_dir = os.path.join(result_dir, latest_folder)
                print(f"选择的文件夹：{folder_name}")
                print(f"最新的子文件夹：{latest_folder}")
                print(f"实际图片路径：{actual_images_dir}")
                # 返回相对于question_dir的路径，用于生成正确的markdown图片路径
                relative_path = os.path.join(folder_name, latest_folder).replace("\\", "/")
                return actual_images_dir, relative_path
            else:
                print(f"错误：{folder_name} 文件夹下没有找到任何图片文件夹！")
                return None, None
    else:
        images_dir = os.path.join(question_dir, "images")
        print(f"选择的文件夹：images")
        print(f"实际图片路径：{images_dir}")
        return images_dir, "images"

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")


def get_response_format_choice(model_id):
    """根据模型ID判断是否支持jsonObject，如果支持则让用户选择response_format"""
    # 支持jsonObject的模型列表（模型1、2、3）
    json_object_supported_models = [
        "doubao-seed-1-6-250615",
        "doubao-seed-1-6-flash-250715",
        "doubao-1-5-thinking-vision-pro-250428"
    ]

    if model_id in json_object_supported_models:
        print("选择response_format：")
        print("1. text")
        print("2. json_object")

        while True:
            user_input = input("请输入选择（1-2）：").strip()
            if user_input == "1":
                return "text"
            elif user_input == "2":
                return "json_object"
            else:
                print("输入无效，请输入 1 或 2")
    else:
        return "text"  # 不支持jsonObject的模型默认使用text


def get_max_tokens_for_model(model_id):
    """根据模型ID返回对应的max_tokens值"""
    if model_id == "doubao-1-5-vision-pro-32k-250115":  # 模型4
        return 12288  # 12K
    else:  # 模型1、2、3
        return 16384  # 16K

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题",
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def process_single_image_api(task):
    """
    只做API推理，输入为task字典（包含base64、图片名、prompt等）
    """
    from volcenginesdkarkruntime import Ark
    import os
    img_filename = task['img_filename']
    base64_image = task['base64_image']
    user_prompt = task['user_prompt']
    client_api_key = task['client_api_key']
    model_id = task.get('model_id', 'doubao-seed-1-6-flash-250715')  # 支持模型参数
    response_format = task.get('response_format', 'text')  # 获取response_format参数
    index = task['index']
    image_path_prefix = task['image_path_prefix']
    # 获取API参数，如果没有传入则使用默认值
    temperature = task.get('temperature', 1)
    top_p = task.get('top_p', 0.7)
    max_tokens_param = task.get('max_tokens')  # 如果没有传入则为None，后面会使用默认逻辑
    # 获取system prompt相关参数
    use_system_prompt = task.get('use_system_prompt', False)
    system_prompt_content = task.get('system_prompt_content')
    sep = f"\n{'=' * 50}\n"
    info = f"处理第 {index} 张图片: {img_filename}"
    current_image_output_lines = []
    current_image_output_lines.append(sep)
    current_image_output_lines.append(info + "\n")
    current_image_output_lines.append(sep)
    current_image_output_lines.append(f"![{img_filename}]({image_path_prefix}{img_filename})\n")
    # 移除控制台输出，改为使用进度条显示
    # print(f"[PID {os.getpid()}] 处理第 {index} 张图片: {img_filename}")

    # 重试机制：最多重试2次，只对Connection error进行重试
    max_retries = 2
    for attempt in range(max_retries + 1):
        try:
            client_local = Ark(
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key=client_api_key,
            )
            # 记录开始时间
            start_time = time.time()

            # 构建用户消息内容（用于实际请求）
            user_content = [
                {"type": "text", "text": user_prompt},
                {"type": "image_url", "image_url": {"url": base64_image, "detail": "high"}}
            ]

            # 构建用户消息内容（用于显示，base64会被省略）
            # 处理base64图片，只保留前10个字符
            display_image_url = base64_image
            if display_image_url.startswith("data:image/") and "," in display_image_url:
                prefix, base64_data = display_image_url.split(",", 1)
                if len(base64_data) > 10:
                    display_image_url = f"{prefix},{base64_data[:10]}...[省略{len(base64_data)-10}个字符]"

            user_content_display = [
                {"type": "text", "text": user_prompt},
                {"type": "image_url", "image_url": {"url": display_image_url, "detail": "high"}}
            ]

            # 使用system prompt工具构建messages（用于实际请求）
            messages = build_messages_with_system_prompt(
                user_content=user_content,
                system_prompt_content=system_prompt_content,
                use_system_prompt=use_system_prompt
            )

            # 使用system prompt工具构建messages（用于显示）
            messages_display = build_messages_with_system_prompt(
                user_content=user_content_display,
                system_prompt_content=system_prompt_content,
                use_system_prompt=use_system_prompt
            )

            # 构建请求参数（先保存未加密版本用于显示）
            request_params_display = {
                "model": model_id,
                "messages": messages_display,
                "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params_display["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params_display["top_p"] = top_p

            # 构建实际请求参数（包含加密头）
            request_params = {
                "model": model_id,
                "messages": messages,
                "extra_headers": {'x-is-encrypted': 'true'},
                "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params["top_p"] = top_p

            # 如果选择了json_object格式，添加response_format参数
            if response_format == "json_object":
                request_params["response_format"] = {"type": "json_object"}
                request_params_display["response_format"] = {"type": "json_object"}

            response = client_local.chat.completions.create(**request_params)
            # 记录结束时间并计算响应时间
            end_time = time.time()
            response_time = end_time - start_time
            resp_content = response.choices[0].message.content.strip()
            if resp_content.startswith("```json"):
                resp_content = resp_content[7:]
            if resp_content.startswith("```"):
                resp_content = resp_content[3:]
            if resp_content.endswith("```"):
                resp_content = resp_content[:-3]
            resp_content = resp_content.strip()

            # 如果是重试成功，添加重试信息
            if attempt > 0:
                current_image_output_lines.append(f"### 重试信息：第 {attempt + 1} 次尝试成功\n")

            current_image_output_lines.append(f"### 响应内容：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{resp_content}\n")
            current_image_output_lines.append("```\n")

            # 添加请求体（使用未加密版本，base64已经在构建时省略）
            current_image_output_lines.append(f"### 请求体：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{json.dumps(request_params_display, ensure_ascii=False, indent=2)}\n")
            current_image_output_lines.append("```\n")

            # 添加响应时间记录
            current_image_output_lines.append(f"### 响应时间：{response_time:.2f}秒\n")
            # token用量信息
            usage = getattr(response, 'usage', None)
            total_tokens = usage.total_tokens if usage and hasattr(usage, 'total_tokens') else None
            cached_tokens = None
            reasoning_tokens = None
            if usage:
                if hasattr(usage, 'prompt_tokens_details') and usage.prompt_tokens_details:
                    cached_tokens = getattr(usage.prompt_tokens_details, 'cached_tokens', None)
                if hasattr(usage, 'completion_tokens_details') and usage.completion_tokens_details:
                    reasoning_tokens = getattr(usage.completion_tokens_details, 'reasoning_tokens', None)
            current_image_output_lines.append("### token用量\n")
            current_image_output_lines.append(f"- total_tokens: {total_tokens}\n")
            current_image_output_lines.append(f"- cached_tokens: {cached_tokens}\n")
            current_image_output_lines.append(f"- reasoning_tokens: {reasoning_tokens}\n")
            return {
                'success': True,
                'image_path': task['image_path'],
                'output_lines': current_image_output_lines,
                'response_content': resp_content
            }
        except Exception as e:
            error_str = str(e)
            # 检查是否为Connection error，如果是且还有重试机会，则继续重试
            if "Connection error" in error_str and attempt < max_retries:
                print(f"[PID {os.getpid()}] 处理图片 {img_filename} 时出现连接错误，正在重试... (第 {attempt + 1} 次尝试)")
                time.sleep(1)  # 等待1秒后重试
                continue

            # 如果不是Connection error或已达到最大重试次数，则返回失败
            err_msg = f"处理图片 {img_filename} 时出错: {error_str}"
            if attempt > 0:
                err_msg += f" (已重试 {attempt} 次)"
            # 只在失败时打印错误信息
            print(f"[PID {os.getpid()}] {err_msg}")
            current_image_output_lines.append(err_msg + "\n")

            # 生成简单的错误JSON格式
            error_json = json.dumps({"请求异常": error_str}, ensure_ascii=False)
            current_image_output_lines.append(f"### 响应内容：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{error_json}\n")
            current_image_output_lines.append("```\n")

            return {
                'success': False,
                'image_path': task['image_path'],
                'output_lines': current_image_output_lines,
                'response_content': error_json
            }

def extract_json_responses_and_names(md_path):
    """提取md文件中所有图片名和响应内容（返回列表: [(img_name, json_str)]）"""
    results = []
    with open(md_path, "r", encoding="utf-8") as f:
        content = f.read()
    # 匹配所有"处理第 n 张图片: xxx.png"
    img_pattern = r"处理第 (\d+) 张图片: ([^\n]+)"
    img_matches = re.findall(img_pattern, content)
    # 匹配所有 ### 响应内容： 后面紧跟的代码块内容（不加粗）
    resp_pattern = r"### 响应内容：\s*```json\s*([\s\S]*?)\s*```"
    resp_matches = re.findall(resp_pattern, content)
    # 标准化json
    norm_jsons = []
    for m in resp_matches:
        json_str = m.strip()
        try:
            obj = json.loads(json_str)
            # 按照题号数字顺序排序，而不是字符串顺序
            def extract_question_number(key):
                """从题号中提取数字部分"""
                import re
                # 匹配数字部分
                match = re.search(r'\d+', str(key))
                if match:
                    return int(match.group())
                return 0  # 如果没有数字，返回0
            
            # 按照题号数字排序
            sorted_items = sorted(obj.items(), key=lambda x: extract_question_number(x[0]))
            sorted_obj = dict(sorted_items)
            norm = json.dumps(sorted_obj, ensure_ascii=False, separators=(',', ':'))
            norm_jsons.append(norm)
        except Exception:
            norm_jsons.append(json_str)
    # 按顺序配对
    for i in range(max(len(img_matches), len(norm_jsons))):
        img_name = img_matches[i][1] if i < len(img_matches) else None
        norm_json = norm_jsons[i] if i < len(norm_jsons) else None
        results.append((img_name, norm_json))
    return results

def run_test(model_id=None, response_format=None, question_type=None, pinyin_name=None, images_dir=None, use_enhance=None, scale=None, use_pixel_connection=None, custom_prompt=None, temperature=None, top_p=None, max_tokens=None, gray_threshold=None, system_prompt=None):
    """
    运行测试，支持从外部传入模型ID和题型
    """
    # 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
    # 初始化Ark客户端，从环境变量中读取您的API Key
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
        api_key="36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b",
    )

    # 如果没有传入模型ID，则获取用户选择的模型
    if model_id is None:
        model_id = get_model_choice()

    # 如果没有传入response_format，则获取用户选择的response_format
    if response_format is None:
        response_format = get_response_format_choice(model_id)

    # 如果没有传入题型，则获取用户选择的题型
    if question_type is None or pinyin_name is None:
        question_type, pinyin_name = get_question_type()

    print(f"使用模型: {model_id}")
    print(f"使用response_format: {response_format}")

    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)

    # 让用户选择图像文件夹
    if images_dir is None:
        images_dir, image_path_prefix = get_folder_choice(question_dir)
        if images_dir is None:
            print("无法获取图像文件夹，程序退出")
            exit()
    else:
        # 检查是否是pixel_enhancement_result相关的路径
        if "pixel_enhancement_result" in images_dir:
            # 如果是pixel_enhancement_result路径，直接使用
            images_dir = os.path.join(question_dir, images_dir)
            # 提取相对路径前缀
            if images_dir.startswith(question_dir):
                image_path_prefix = images_dir[len(question_dir):].replace("\\", "/")
                if image_path_prefix.startswith("/"):
                    image_path_prefix = image_path_prefix[1:]
            else:
                image_path_prefix = images_dir.replace("\\", "/")
        else:
            # 其他情况使用原有逻辑
            images_dir = os.path.join(question_dir, images_dir)
            image_path_prefix = images_dir.replace(question_dir, "").replace("\\", "/")
        
        print(f"使用外部传入的图片文件夹：{images_dir}")
        print(f"实际图片路径前缀：{image_path_prefix}")

    response_dir = os.path.join(question_dir, "response")
    prompt_file = os.path.join(question_dir, "prompt.md")

    # 新增：让用户选择是否采用"灰度阀门与像素增强"处理（仅在未提供参数时询问）
    if use_enhance is None:
        while True:
            enhance_input = input("是否采用'灰度阀门与像素增强'处理？(y/n)：").strip().lower()
            if enhance_input in ("y", "n"):
                use_enhance = (enhance_input == "y")
                break
            else:
                print("请输入 y 或 n")

    # 新增：如果选择了灰度阀门与像素增强，询问是否采用黑色像素粘连
    if use_pixel_connection is None:
        use_pixel_connection = False
        if use_enhance:
            while True:
                connection_input = input("是否采用'黑色像素粘连'处理？(y/n)：").strip().lower()
                if connection_input in ("y", "n"):
                    use_pixel_connection = (connection_input == "y")
                    break
                else:
                    print("请输入 y 或 n")

    # 新增：让用户输入放大倍数（仅在未提供参数时询问）
    if scale is None:
        while True:
            try:
                scale_input = input("请输入图片放大倍数（如2、4、6、8）：").strip()
                scale = float(scale_input)
                if scale <= 0:
                    print("放大倍数必须为正数！")
                    continue
                break
            except Exception:
                print("请输入有效的数字倍数！")

    # 处理灰度阀门参数
    if gray_threshold is None:
        gray_threshold = 200  # 使用默认值200
    else:
        # 验证灰度阀门值是否在有效范围内
        if not isinstance(gray_threshold, (int, float)) or not (0 <= gray_threshold <= 255):
            print(f"警告：灰度阀门值 {gray_threshold} 无效，必须在0-255范围内，将使用默认值200")
            gray_threshold = 200

    # 创建error文件夹，用于存储错误汇总文件
    error_dir = os.path.join(question_dir, "error")
    os.makedirs(error_dir, exist_ok=True)

    print(f"\n使用路径：")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{response_dir}")
    print(f"提示词文件：{prompt_file}")
    print(f"错误文件夹：{error_dir}")

    # 检查并创建必要的目录
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(response_dir, exist_ok=True)

    # 优先使用自定义prompt，否则从prompt.md文件读取提示词
    if custom_prompt:
        user_prompt = custom_prompt
        print("使用从main脚本传递的自定义提示词")
    else:
        if not os.path.exists(prompt_file):
            print(f"错误：提示词文件 {prompt_file} 不存在！")
            print(f"请创建 {prompt_file} 文件并写入提示词内容")
            exit()

        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                markdown_prompt = f.read().strip()
            print(f"已从文件 {prompt_file} 读取提示词")
            # 将markdown格式转换为纯文本
            user_prompt = markdown_to_text(markdown_prompt)
            print("已将markdown格式转换为纯文本")
        except Exception as e:
            print(f"读取提示词文件时出错：{str(e)}")
            exit()

        if not user_prompt:
            print("错误：提示词文件为空！")
            exit()

    # 获取system prompt配置
    if system_prompt is not None:
        # 从main.py传入的system_prompt参数
        use_system_prompt = True
        system_prompt_content = system_prompt
    else:
        # 判断是否为批处理模式：如果所有关键参数都不是None，说明是从main.py调用的
        is_batch_mode = (model_id is not None and question_type is not None and images_dir is not None and custom_prompt is not None)
        use_system_prompt, system_prompt_content = get_system_prompt_config(interactive=not is_batch_mode)

    # 获取images文件夹中的所有图片文件
    image_files = get_image_files(images_dir)

    # 生成 response 文件夹和文件名
    now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    response_file = os.path.join(response_dir, f"{now}.md")
    output_lines = []

    # 新增：如果 use_enhance 为 True，最顶部插入说明
    if use_enhance:
        if use_pixel_connection:
            output_lines.append("使用'灰度阀门与像素增强'处理（含黑色像素粘连）\n\n")
        else:
            output_lines.append("使用'灰度阀门与像素增强'处理（不含黑色像素粘连）\n\n")

    # 预留准确率位置（稍后会在文件开头插入）
    output_lines.append(f"# 运行时间: {now}\n\n")
    output_lines.append(f"## 使用模型ID: {model_id}\n\n")
    output_lines.append(f"## 使用图片文件夹: {image_path_prefix}\n\n")
    output_lines.append(f"## 图片放大倍数: {scale}\n\n")

    # 添加使用的提示词
    output_lines.append(f"## 使用的提示词\n\n")
    output_lines.append(f"{user_prompt}\n\n")

    if not image_files:
        msg1 = "images文件夹中没有找到图片文件！"
        msg2 = "支持的格式：.jpg, .jpeg, .png, .gif, .webp"
        print(msg1)
        print(msg2)
        output_lines.append(msg1 + "\n")
        output_lines.append(msg2 + "\n")
        all_processed_results = []
    else:
        msg1 = f"找到 {len(image_files)} 张图片，开始逐个处理..."
        msg2 = f"使用的提示词: {user_prompt}"
        print(msg1)
        print(msg2)
        output_lines.append(msg1 + "\n")
        output_lines.append(msg2 + "\n")
        print("\n--- 开始本地处理图片（增强/缩放/编码） ---\n")
        api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"

        # 准备本地处理任务
        local_tasks = []
        for i, image_path in enumerate(image_files):
            img_filename = os.path.basename(image_path)
            local_tasks.append({
                'image_path': image_path,
                'img_filename': img_filename,
                'use_enhance': use_enhance,
                'enhance_threshold': gray_threshold,
                'scale': scale,
                'use_pixel_connection': use_pixel_connection
            })

        # 使用多进程进行本地图片处理
        num_processes = os.cpu_count() if os.cpu_count() else 4
        print(f"正在使用 {num_processes} 个进程进行本地图片处理...")

        local_results = []
        with multiprocessing.Pool(processes=num_processes) as pool:
            # 使用imap代替map以支持进度条
            for result in tqdm(pool.imap(process_single_image_local, local_tasks),
                             total=len(local_tasks), desc="本地处理", unit="张"):
                local_results.append(result)

        # 准备API推理任务，只包含成功处理的图片
        api_tasks = []
        for i, local_result in enumerate(local_results):
            if local_result['success']:
                api_tasks.append({
                    'img_filename': local_result['img_filename'],
                    'base64_image': local_result['base64_image'],
                    'user_prompt': user_prompt,
                    'client_api_key': api_key_from_client_init,
                    'model_id': model_id,  # 添加模型ID参数
                    'response_format': response_format,  # 添加response_format参数
                    'index': i + 1,
                    'image_path_prefix': f"../{image_path_prefix}/",
                    'image_path': local_result['image_path'],
                    'temperature': temperature,  # 添加temperature参数
                    'top_p': top_p,  # 添加top_p参数
                    'max_tokens': max_tokens,  # 添加max_tokens参数
                    'use_system_prompt': use_system_prompt,  # 添加system prompt使用标志
                    'system_prompt_content': system_prompt_content  # 添加system prompt内容
                })
            else:
                print(f"跳过处理失败的图片: {local_result['img_filename']}")

        print(f"本地处理完成: {len(api_tasks)}/{len(local_tasks)} 张图片成功处理")
        print(f"\n--- 开始并行API推理 ---\n")
        print(f"将使用 {num_processes} 个进程进行并行API推理。")

        # 使用进度条显示API推理进度
        all_processed_results = []
        with multiprocessing.Pool(processes=num_processes) as pool:
            # 使用imap代替map以支持进度条
            for result in tqdm(pool.imap(process_single_image_api, api_tasks),
                             total=len(api_tasks), desc="API推理", unit="张"):
                all_processed_results.append(result)
        print("\n--- 并行API推理完成，合并结果 ---\n")
        for result in all_processed_results:
            output_lines.extend(result['output_lines'])
        sep = f"\n{'=' * 50}\n"
        output_lines.append(sep)
        output_lines.append("所有图片处理完成！\n")
        output_lines.append(sep)
        print(sep)
        print("所有图片处理完成！")
        print(sep)

    # 写入新内容到新文件
    with open(response_file, "w", encoding="utf-8") as f:
        f.writelines(output_lines)

    # 如果没有response_template.md，则将本次md文件复制为模板
    template_path = os.path.join(response_dir, "response_template.md")
    if not os.path.exists(template_path):
        shutil.copyfile(response_file, template_path)

    # 比对错题功能
    template_path = os.path.join(response_dir, "response_template.md")
    wrong_items = []
    accuracy_str = ""
    if os.path.exists(template_path):
        new_results = extract_json_responses_and_names(response_file)
        template_results = extract_json_responses_and_names(template_path)
        wrongs = []
        error_msgs = []
        # 检查图片数量
        if len(new_results) != len(template_results):
            error_msgs.append(f"图片数量不一致：本次{len(new_results)}，模板{len(template_results)}")
        # 检查图片名和内容
        min_len = min(len(new_results), len(template_results))
        for i in range(min_len):
            new_name, new_json = new_results[i]
            tpl_name, tpl_json = template_results[i]

            # 提取文件名前缀进行比较（去除扩展名和可能的后缀）
            def get_file_prefix(filename):
                if filename is None:
                    return None
                # 去除扩展名
                name_without_ext = os.path.splitext(filename)[0]
                # 如果包含下划线，可能有后缀，取第一个下划线前的部分作为基础名
                # 但要考虑原始文件名本身就包含下划线的情况
                # 这里采用更保守的策略：如果模板文件名在当前文件名的开头，则认为匹配
                return name_without_ext

            new_prefix = get_file_prefix(new_name)
            tpl_prefix = get_file_prefix(tpl_name)

            # 检查文件名是否匹配（模板文件名应该是当前文件名的前缀）
            files_match = False
            if tpl_prefix and new_prefix:
                # 如果模板文件名是当前文件名的前缀，则认为匹配
                if new_prefix.startswith(tpl_prefix):
                    files_match = True
                # 或者如果完全相同也匹配
                elif new_prefix == tpl_prefix:
                    files_match = True

            if not files_match:
                wrongs.append(f"第 {i+1} 张图片: 文件名不一致，本次为 {new_name}，模板为 {tpl_name}")
                wrong_items.append(i+1)
            elif new_json != tpl_json:
                # 检查模板中的JSON是否为{}，如果是则跳过该题目的比较
                if tpl_json == "{}":
                    print(f"第 {i+1} 张图片: {new_name} - 模板JSON为空，跳过比较")
                    continue
                else:
                    # 在比较前，对两个JSON进行值序列比较
                    def compare_json_values(json_str1, json_str2):
                        """比较两个JSON的值序列，忽略键名差异"""
                        # 首先检查是否有None值
                        if json_str1 is None or json_str2 is None:
                            return json_str1 == json_str2

                        try:
                            obj1 = json.loads(json_str1)
                            obj2 = json.loads(json_str2)

                            # 按照题号数字顺序排序并提取值
                            def extract_question_number(key):
                                import re
                                match = re.search(r'\d+', str(key))
                                if match:
                                    return int(match.group())
                                return 0

                            # 对两个JSON对象按题号排序并提取值序列
                            sorted_items1 = sorted(obj1.items(), key=lambda x: extract_question_number(x[0]))
                            sorted_items2 = sorted(obj2.items(), key=lambda x: extract_question_number(x[0]))

                            values1 = [item[1] for item in sorted_items1]
                            values2 = [item[1] for item in sorted_items2]

                            return values1 == values2
                        except:
                            # 如果JSON解析失败，回退到字符串比较
                            # 再次检查None值以避免AttributeError
                            if json_str1 is None or json_str2 is None:
                                return json_str1 == json_str2
                            return json_str1.strip() == json_str2.strip()

                    if not compare_json_values(new_json, tpl_json):
                        wrongs.append(f"第 {i+1} 张图片: {new_name}")
                        wrong_items.append(i+1)
                        
        # 统计准确率
        total = min_len
        wrong_count = len(wrongs)
        accuracy = (total - wrong_count) / total if total > 0 else 1.0
        accuracy_str = f"准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n"

        # 保存准确率信息供返回使用
        accuracy_info = {
            'accuracy': accuracy,
            'accuracy_str': accuracy_str.strip(),
            'total': total,
            'wrong_count': wrong_count,
            'accuracy_percentage': f"{(accuracy*100):.2f}%"
        }

        # 构造新内容 - 按照新的格式要求：准确率置顶，然后是错题
        # 1. 准确率置顶
        new_top = f"## {accuracy_str}\n"

        # 2. 添加模型ID和图片文件夹信息
        model_line = f"**使用模型ID：** {model_id}\n\n"
        folder_line = f"**使用图片文件夹：** {image_path_prefix}\n\n"
        new_top += model_line + folder_line

        # 3. 错题信息
        new_top += "## 错题\n"
        if error_msgs:
            for msg in error_msgs:
                new_top += f"- {msg}\n"
        if wrongs:
            for w in wrongs:
                new_top += f"- {w}\n"
        if not error_msgs and not wrongs:
            new_top += "本次无错题。\n"
        new_top += "\n"

        # 读取原有内容
        with open(response_file, "r", encoding="utf-8") as f:
            old_content = f.read()
        # 写回文件，准确率和错题在最前面
        with open(response_file, "w", encoding="utf-8") as f:
            f.write(new_top)
            f.write(old_content)
        # 控制台输出（只打印准确率，不打印详细错题列表）
        console_output = f"## {accuracy_str}\n"
        console_output += model_line + folder_line
        if not error_msgs and not wrongs:
            console_output += "## 错题\n本次无错题。\n"
        else:
            console_output += f"## 错题\n共 {len(wrongs) + len(error_msgs)} 项错题（详细信息已保存到文件）\n"
        print(console_output)
        
        # 创建错题汇总文件
        if wrong_items:
            current_date = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            summary_file = os.path.join(error_dir, f"error_summary_{current_date}.md")
            summary_lines = []
            # 头部：准确率、运行时间置顶，然后是模型ID和图片文件夹信息，最后是错题列表
            summary_lines.append(f"## 准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n\n")
            summary_lines.append(f"## 运行时间: {now}\n\n")
            # 添加模型ID和图片文件夹信息
            model_line2 = f"**使用模型ID：** {model_id}\n\n"
            folder_line2 = f"**使用图片文件夹：** {image_path_prefix}\n\n"
            summary_lines.append(model_line2)
            summary_lines.append(folder_line2)
            # 错题列表
            summary_lines.append(f"## 错题\n\n")
            for item in wrong_items:
                if item <= len(image_files):
                    img_name = os.path.basename(image_files[item-1])
                    summary_lines.append(f"- 第 {item} 张图片: {img_name}\n")
            # 只插入错题的详细内容块，格式与response下md完全一致
            for idx in wrong_items:
                if idx <= len(all_processed_results):
                    result = all_processed_results[idx-1]
                    # 修改图片路径，引用images文件夹内的图片
                    modified_output_lines = []
                    template_json = None

                    # 获取对应的模板JSON答案
                    if idx <= len(template_results):
                        template_json = template_results[idx-1][1]

                    for line in result['output_lines']:
                        # 将相对路径替换为引用images文件夹的路径
                        if f'../{image_path_prefix}/' in line:
                            line = line.replace(f'../{image_path_prefix}/', f'../images/')
                        modified_output_lines.append(line)

                        # 在图片显示后、模型回答前插入模板答案
                        if line.startswith(f"![{os.path.basename(image_files[idx-1])}]"):
                            # 添加模板答案部分
                            if template_json and template_json != "{}":
                                modified_output_lines.append("\n### response_template答案：\n")
                                modified_output_lines.append("```json\n")
                                modified_output_lines.append(f"{template_json}\n")
                                modified_output_lines.append("```\n")

                    summary_lines.extend(modified_output_lines)
            summary_lines.append("\n==================================================\n所有错题处理完成！\n==================================================\n")
            with open(summary_file, "w", encoding="utf-8") as f:
                f.writelines(summary_lines)
            print(f"已创建错题详细 summary.md 文件: {summary_file}")

    print(f"结果已保存到：{response_file}")

    # 如果有准确率信息，返回准确率信息，否则返回默认值
    if 'accuracy_info' in locals():
        return True, accuracy_info  # 返回成功状态和准确率信息
    else:
        # 如果没有模板文件，返回默认准确率信息
        default_accuracy_info = {
            'accuracy': 0.0,
            'accuracy_str': '准确率：未知（无模板文件）',
            'total': 0,
            'wrong_count': 0,
            'accuracy_percentage': '未知'
        }
        return True, default_accuracy_info

if __name__ == "__main__":
    import argparse
    import json

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='运行test.py脚本')
    parser.add_argument('--stage', choices=['single', 'dual'], default='single',
                       help='运行模式：single=单阶段，dual=双阶段')
    args = parser.parse_args()

    if args.stage == 'dual':
        # 双阶段模式：运行test并保存配置
        print("双阶段模式：运行第一阶段并保存配置...")

        # 获取用户选择的模型
        model_id = get_model_choice()

        # 获取用户选择的response_format
        response_format = get_response_format_choice(model_id)

        # 获取用户选择的题型
        question_type, pinyin_name = get_question_type()

        # 构建题型相关的路径
        types_dir = "types"
        question_dir = os.path.join(types_dir, pinyin_name)

        # 让用户选择图像文件夹
        images_dir, image_path_prefix = get_folder_choice(question_dir)
        if images_dir is None:
            print("无法获取图像文件夹，程序退出")
            sys.exit(1)

        # 让用户选择是否采用"灰度阀门与像素增强"处理
        while True:
            enhance_input = input("是否采用'灰度阀门与像素增强'处理？(y/n)：").strip().lower()
            if enhance_input in ("y", "n"):
                use_enhance = (enhance_input == "y")
                break
            else:
                print("请输入 y 或 n")

        # 如果选择了灰度阀门与像素增强，询问是否采用黑色像素粘连
        use_pixel_connection = False
        if use_enhance:
            while True:
                connection_input = input("是否采用'黑色像素粘连'处理？(y/n)：").strip().lower()
                if connection_input in ("y", "n"):
                    use_pixel_connection = (connection_input == "y")
                    break
                else:
                    print("请输入 y 或 n")

        # 让用户输入放大倍数
        while True:
            try:
                scale_input = input("请输入图片放大倍数（如2、4、6、8）：").strip()
                scale = float(scale_input)
                if scale <= 0:
                    print("放大倍数必须为正数！")
                    continue
                break
            except Exception:
                print("请输入有效的数字倍数！")

        # 保存配置到文件
        # 注意：保存原始的文件夹名，而不是完整路径
        original_images_dir = image_path_prefix.strip('/')
        config = {
            'model_id': model_id,
            'response_format': response_format,
            'question_type': question_type,
            'pinyin_name': pinyin_name,
            'images_dir': original_images_dir,
            'image_path_prefix': image_path_prefix,
            'use_enhance': use_enhance,
            'scale': scale,
            'question_dir': question_dir,
            'use_pixel_connection': use_pixel_connection
        }

        with open('temp_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        print(f"配置已保存：模型={model_id}, response_format={response_format}, 题型={question_type}, 增强={use_enhance}, 像素粘连={use_pixel_connection}, 倍数={scale}")

        # 运行test处理
        run_test(model_id=model_id, response_format=response_format, question_type=question_type, pinyin_name=pinyin_name,
                images_dir=image_path_prefix.strip('/'), use_enhance=use_enhance, scale=scale, use_pixel_connection=use_pixel_connection)
    else:
        # 单阶段模式：正常运行
        run_test()